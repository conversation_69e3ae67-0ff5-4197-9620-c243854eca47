@echo off
echo Starting BongoCat UI Debug...
echo Current directory: %CD%
echo.

echo Checking file existence...
if exist "BongoCatUI.exe" (
    echo BongoCatUI.exe found
) else (
    echo BongoCatUI.exe NOT found
    pause
    exit /b 1
)

if exist "BongoCatMverUI.dll" (
    echo BongoCatMverUI.dll found
) else (
    echo BongoCatMverUI.dll NOT found
)

if exist "MaterialDesignThemes.Wpf.dll" (
    echo MaterialDesignThemes.Wpf.dll found
) else (
    echo MaterialDesignThemes.Wpf.dll NOT found
)

if exist "MaterialDesignColors.dll" (
    echo MaterialDesignColors.dll found
) else (
    echo MaterialDesignColors.dll NOT found
)

if exist "config.json" (
    echo config.json found
) else (
    echo config.json NOT found
)

echo.
echo Attempting to run BongoCatUI.exe...
echo Press any key to continue...
pause > nul

echo.
echo Running BongoCatUI.exe with error capture...
BongoCatUI.exe 2>&1
set ERRORLEVEL_RESULT=%ERRORLEVEL%

echo.
echo Program exited with code: %ERRORLEVEL_RESULT%
echo.

if %ERRORLEVEL_RESULT% NEQ 0 (
    echo Error detected. Checking Windows Event Log...
    powershell -Command "Get-WinEvent -FilterHashtable @{LogName='Application'; Level=2} -MaxEvents 3 | Where-Object {$_.TimeCreated -gt (Get-Date).AddMinutes(-2)} | Format-Table TimeCreated, Id, LevelDisplayName, Message -Wrap"
)

echo.
echo Debug complete. Press any key to exit...
pause > nul
